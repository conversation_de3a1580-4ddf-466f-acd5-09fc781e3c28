#Requires AutoHotkey v2.0

; ===================================================================
; WinCBT-Biometric Verify Mode Utilities
; Provides verification-only mode without seat assignment
; ===================================================================

class VerifyModeManager {
    ; Static properties - initialized in Initialize method
    static isVerifyMode := false
    static verifyModePassword := "verify2024"
    static verifyModeEnabled := false
    static initialized := false

    ; Safe integer reading function
    static SafeReadInteger(file, section, key, defaultValue) {
        try {
            value := IniRead(file, section, key, defaultValue)
            return Integer(value)
        } catch {
            return Integer(defaultValue)
        }
    }

    ; Initialize verify mode
    static Initialize() {
        try {
            ; Prevent multiple initialization
            if (this.initialized) {
                return true
            }

            ; Initialize static properties
            this.isVerifyMode := false
            this.verifyModePassword := "verify2024"

            ; Read verify mode settings from config
            configFile := PathManager.ConfigFile
            this.verifyModeEnabled := this.SafeReadInteger(configFile, "Verification", "EnableVerifyMode", "0")

            if (this.verifyModeEnabled) {
                ErrorHandler.LogMessage("INFO", "Verify Mode is enabled in configuration")
            }

            this.initialized := true
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Failed to initialize Verify Mode: " err.Message)
            return false
        }
    }

    ; Toggle verify mode on/off
    static ToggleVerifyMode() {
        try {
            ; Check if verify mode is enabled in config
            if (!this.verifyModeEnabled) {
                MsgBox("Verify Mode is disabled in configuration.", "Verify Mode Disabled", "Icon!")
                return false
            }

            if (this.isVerifyMode) {
                ; Turning off verify mode
                this.isVerifyMode := false
                ErrorHandler.LogMessage("INFO", "Verify Mode disabled")
                return true
            } else {
                ; Turning on verify mode - require password
                passwordResult := this.ShowVerifyModePasswordDialog()

                if (passwordResult == "SUCCESS") {
                    this.isVerifyMode := true
                    ErrorHandler.LogMessage("INFO", "Verify Mode enabled")
                    return true
                } else {
                    ErrorHandler.LogMessage("WARNING", "Verify Mode activation cancelled or failed")
                    return false
                }
            }
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Error toggling Verify Mode: " err.Message)
            return false
        }
    }

    ; Show password dialog for verify mode
    static ShowVerifyModePasswordDialog() {
        global myGui

        ; Disable the main GUI while password dialog is open
        myGui.Opt("+Disabled")

        ; Create the password dialog
        passwordGui := Gui("+AlwaysOnTop +Owner" myGui.Hwnd " -MinimizeBox -MaximizeBox -SysMenu", "Verify Mode Authentication")
        passwordGui.SetFont("s10", "Segoe UI")
        passwordGui.BackColor := 0xF0FFF0  ; Honeydew background

        ; Add title
        titleText := passwordGui.Add("Text", "x20 y20 w360 h30 Center", "Enter Verify Mode Password")
        titleText.SetFont("s12 Bold", "Segoe UI")
        titleText.Opt("cDarkGreen")

        ; Add description
        descText := passwordGui.Add("Text", "x20 y55 w360 h40 Center", "Verify Mode allows biometric verification without seat assignment")
        descText.SetFont("s9", "Segoe UI")
        descText.Opt("c0x666666")

        ; Add password field
        passwordLabel := passwordGui.Add("Text", "x20 y105 w80 h20", "Password:")
        passwordLabel.SetFont("s10", "Segoe UI")
        passwordEdit := passwordGui.Add("Edit", "x110 y105 w200 h25 Password")
        passwordEdit.SetFont("s10", "Segoe UI")

        ; Add error message (initially hidden)
        errorText := passwordGui.Add("Text", "x20 y140 w360 h20 Center Hidden", "Incorrect password. Please try again.")
        errorText.SetFont("s9", "Segoe UI")
        errorText.Opt("cRed")

        ; Add buttons
        okButton := passwordGui.Add("Button", "x110 y170 w80 h30 +0x1000", "OK")
        okButton.SetFont("s10", "Segoe UI")
        cancelButton := passwordGui.Add("Button", "x210 y170 w80 h30 +0x1000", "Cancel")
        cancelButton.SetFont("s10", "Segoe UI")

        ; Dialog result variable
        dialogResult := "CANCELLED"

        ; Define OK button click handler
        OkButtonClick(*) {
            enteredPassword := passwordEdit.Text

            if (enteredPassword == this.verifyModePassword) {
                dialogResult := "SUCCESS"
                ClosePasswordDialog()
            } else {
                errorText.Opt("-Hidden")
                passwordEdit.Text := ""
                passwordEdit.Focus()
                ErrorHandler.LogMessage("WARNING", "Incorrect password entered in Verify Mode dialog")
            }
        }

        ; Define Cancel button click handler
        CancelButtonClick(*) {
            dialogResult := "CANCELLED"
            ClosePasswordDialog()
        }

        ; Define close dialog function
        ClosePasswordDialog() {
            myGui.Opt("-Disabled")
            passwordGui.Destroy()
            ErrorHandler.LogMessage("INFO", "Verify Mode password dialog closed with result: " dialogResult)
        }

        ; Set button events
        okButton.OnEvent("Click", OkButtonClick)
        cancelButton.OnEvent("Click", CancelButtonClick)

        ; Handle Enter key in password field
        HandleEnterKey(*) {
            if (GetKeyState("Enter", "P")) {
                OkButtonClick()
            }
        }
        passwordEdit.OnEvent("Change", HandleEnterKey)

        ; Show the dialog and focus on password field
        passwordGui.Show("w400 h235")
        passwordEdit.Focus()

        ; Wait for the dialog to close and return the result
        WinWaitClose("ahk_id " passwordGui.Hwnd)
        return dialogResult
    }

    ; Check if currently in verify mode
    static IsVerifyMode() {
        return this.isVerifyMode
    }

    ; Update verification status for verify mode
    static UpdateVerifyModeStatus(rollNumber, biometricType, status, confidence := "") {
        try {
            ; Get database file path
            candidatesPath := PathManager.GetDatabaseFilePath("Candidates")

            ; Determine the field name based on biometric type
            fieldName := "Verify" biometricType "Status"
            confidenceField := "Verify" biometricType "Confidence"

            ; Write the status to the database
            IniWrite(status, candidatesPath, rollNumber, fieldName)

            ; Write confidence if provided
            if (confidence != "") {
                IniWrite(confidence, candidatesPath, rollNumber, confidenceField)
            }

            ; Log the verification
            ErrorHandler.LogMessage("INFO", "Verify Mode - Updated " fieldName " to " status " for " rollNumber)

            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Error updating verify mode status: " err.Message)
            return false
        }
    }

    ; Check if all required verifications are complete in verify mode
    static CheckVerifyModeVerifications(rollNumber) {
        try {
            ; Get database file path
            candidatesPath := PathManager.GetDatabaseFilePath("Candidates")

            ; Read verification settings
            configFile := PathManager.ConfigFile
            signatureVerificationEnabled := this.SafeReadInteger(configFile, "Verification", "SignatureVerification", "1")
            rightThumbprintVerificationEnabled := this.SafeReadInteger(configFile, "Verification", "RightThumbprintVerification", "1")

            ; Check photo verification
            photoStatus := IniRead(candidatesPath, rollNumber, "VerifyPhotoStatus", "")
            photoComplete := (photoStatus == "Verified")

            ; Check fingerprint verification
            fingerprintStatus := IniRead(candidatesPath, rollNumber, "VerifyFingerprintStatus", "")
            fingerprintComplete := (fingerprintStatus == "Verified")

            ; Check right fingerprint if enabled
            rightFingerprintComplete := true
            if (rightThumbprintVerificationEnabled) {
                rightFingerprintStatus := IniRead(candidatesPath, rollNumber, "VerifyRightFingerprintStatus", "")
                rightFingerprintComplete := (rightFingerprintStatus == "Verified")
            }

            ; Check signature verification if enabled
            signatureComplete := true
            if (signatureVerificationEnabled) {
                signatureStatus := IniRead(candidatesPath, rollNumber, "VerifySignatureStatus", "")
                signatureComplete := (signatureStatus == "Verified")
            }

            ; All required verifications must be complete
            allComplete := photoComplete && fingerprintComplete && rightFingerprintComplete && signatureComplete

            ; Update overall verification status
            if (allComplete) {
                IniWrite("Verified", candidatesPath, rollNumber, "VerifyBiometricStatus")
                ErrorHandler.LogMessage("INFO", "All verify mode verifications complete for " rollNumber)
            } else {
                IniWrite("Incomplete", candidatesPath, rollNumber, "VerifyBiometricStatus")
                ErrorHandler.LogMessage("INFO", "Verify mode verifications incomplete for " rollNumber)
            }

            return allComplete
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Error checking verify mode verifications: " err.Message)
            return false
        }
    }

    ; Get verify mode status for display
    static GetVerifyModeStatus(rollNumber) {
        try {
            candidatesPath := PathManager.GetDatabaseFilePath("Candidates")

            status := Map()
            status["Photo"] := IniRead(candidatesPath, rollNumber, "VerifyPhotoStatus", "")
            status["Fingerprint"] := IniRead(candidatesPath, rollNumber, "VerifyFingerprintStatus", "")
            status["RightFingerprint"] := IniRead(candidatesPath, rollNumber, "VerifyRightFingerprintStatus", "")
            status["Signature"] := IniRead(candidatesPath, rollNumber, "VerifySignatureStatus", "")
            status["Overall"] := IniRead(candidatesPath, rollNumber, "VerifyBiometricStatus", "")

            return status
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Error getting verify mode status: " err.Message)
            return Map()
        }
    }

    ; Clear verify mode data for a candidate
    static ClearVerifyModeData(rollNumber) {
        try {
            candidatesPath := PathManager.GetDatabaseFilePath("Candidates")

            ; Clear all verify mode status fields
            IniDelete(candidatesPath, rollNumber, "VerifyPhotoStatus")
            IniDelete(candidatesPath, rollNumber, "VerifyFingerprintStatus")
            IniDelete(candidatesPath, rollNumber, "VerifyRightFingerprintStatus")
            IniDelete(candidatesPath, rollNumber, "VerifySignatureStatus")
            IniDelete(candidatesPath, rollNumber, "VerifyBiometricStatus")

            ; Clear confidence fields
            IniDelete(candidatesPath, rollNumber, "VerifyPhotoConfidence")
            IniDelete(candidatesPath, rollNumber, "VerifyFingerprintConfidence")
            IniDelete(candidatesPath, rollNumber, "VerifyRightFingerprintConfidence")
            IniDelete(candidatesPath, rollNumber, "VerifySignatureConfidence")

            ErrorHandler.LogMessage("INFO", "Cleared verify mode data for " rollNumber)
            return true
        } catch as err {
            ErrorHandler.LogMessage("ERROR", "Error clearing verify mode data: " err.Message)
            return false
        }
    }
}
