#Requires AutoHotkey v2.0

; Test script for the new VerificationEngine
; This script tests the data-driven verification logic

; Include required files
#Include "lib\path_manager.ahk"
#Include "lib\error_handler.ahk"
#Include "lib\verification_engine.ahk"

; Initialize test environment
global g_isPostExamMode := false
global g_isVerifyMode := false
global SignatureVerificationEnabled := true

; Mock candidate data for testing
class TestCandidateData {
    static CreateTestCandidate(rollNumber, thumbPreference := "Both") {
        candidateData := Map()
        candidateData["Name"] := "Test Candidate " rollNumber
        candidateData["ThumbPreference"] := thumbPreference
        candidateData["PhotoStatus"] := ""
        candidateData["FingerprintStatus"] := ""
        candidateData["RightFingerprintStatus"] := ""
        candidateData["SignatureStatus"] := ""
        candidateData["BiometricStatus"] := ""
        return candidateData
    }
}

; Mock LoadCandidateData function
LoadCandidateData(rollNumber) {
    ; Create test candidate with different ThumbPreference values
    if (rollNumber == "TEST001") {
        return TestCandidateData.CreateTestCandidate(rollNumber, "Both")
    } else if (rollNumber == "TEST002") {
        return TestCandidateData.CreateTestCandidate(rollNumber, "Left")
    } else if (rollNumber == "TEST003") {
        return TestCandidateData.CreateTestCandidate(rollNumber, "Right")
    } else {
        return TestCandidateData.CreateTestCandidate(rollNumber, "Both")
    }
}

; Test function to verify ThumbPreference logic
TestThumbPreferenceLogic() {
    OutputDebug("=== Testing ThumbPreference Logic ===")
    
    ; Test Case 1: ThumbPreference = "Both"
    testData1 := TestCandidateData.CreateTestCandidate("TEST001", "Both")
    testData1["FingerprintStatus"] := "Saved"
    testData1["RightFingerprintStatus"] := "Saved"
    result1 := VerificationEngine.CheckThumbprintVerification(testData1)
    OutputDebug("Test 1 - Both thumbs required, both saved: " (result1 ? "PASS" : "FAIL"))
    
    ; Test Case 2: ThumbPreference = "Both", only left saved
    testData2 := TestCandidateData.CreateTestCandidate("TEST001", "Both")
    testData2["FingerprintStatus"] := "Saved"
    testData2["RightFingerprintStatus"] := ""
    result2 := VerificationEngine.CheckThumbprintVerification(testData2)
    OutputDebug("Test 2 - Both thumbs required, only left saved: " (result2 ? "FAIL" : "PASS"))
    
    ; Test Case 3: ThumbPreference = "Left"
    testData3 := TestCandidateData.CreateTestCandidate("TEST002", "Left")
    testData3["FingerprintStatus"] := "Saved"
    testData3["RightFingerprintStatus"] := ""
    result3 := VerificationEngine.CheckThumbprintVerification(testData3)
    OutputDebug("Test 3 - Left thumb only required, left saved: " (result3 ? "PASS" : "FAIL"))
    
    ; Test Case 4: ThumbPreference = "Right"
    testData4 := TestCandidateData.CreateTestCandidate("TEST003", "Right")
    testData4["FingerprintStatus"] := ""
    testData4["RightFingerprintStatus"] := "Saved"
    result4 := VerificationEngine.CheckThumbprintVerification(testData4)
    OutputDebug("Test 4 - Right thumb only required, right saved: " (result4 ? "PASS" : "FAIL"))
    
    ; Test Case 5: ThumbPreference = "Right", but only left saved
    testData5 := TestCandidateData.CreateTestCandidate("TEST003", "Right")
    testData5["FingerprintStatus"] := "Saved"
    testData5["RightFingerprintStatus"] := ""
    result5 := VerificationEngine.CheckThumbprintVerification(testData5)
    OutputDebug("Test 5 - Right thumb only required, only left saved: " (result5 ? "FAIL" : "PASS"))
    
    OutputDebug("=== ThumbPreference Logic Tests Complete ===")
}

; Test function to verify overall verification logic
TestOverallVerificationLogic() {
    OutputDebug("=== Testing Overall Verification Logic ===")
    
    ; Note: This would require actual database files to test properly
    ; For now, we'll just test the thumbprint logic which is the core issue
    
    OutputDebug("=== Overall Verification Logic Tests Complete ===")
}

; Main test function
RunTests() {
    try {
        ; Initialize error handler
        ErrorHandler.Initialize()
        
        ; Run tests
        TestThumbPreferenceLogic()
        TestOverallVerificationLogic()
        
        OutputDebug("=== All Tests Complete ===")
        MsgBox("Verification Engine tests completed. Check DebugView for results.", "Test Complete")
        
    } catch as err {
        OutputDebug("Test error: " err.Message)
        MsgBox("Test error: " err.Message, "Test Error")
    }
}

; Run the tests
RunTests()
